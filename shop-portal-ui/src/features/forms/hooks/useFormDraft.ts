import { useRegisterUserData } from '@/features/user-data/hooks/useRegisterUserData'
import { useRemoveUserData } from '@/features/user-data/hooks/useRemoveUserData'
import { useUserData } from '@/features/user-data/hooks/useUserData'
import { UserDataEntry } from '@/features/user-data/types/rxdb'
import { useAppContext } from '@/providers/AppProvider'
import { useMRAttributesByLocation } from '@/providers/MRAttributesProvider'
import { useCallback, useMemo, useRef } from 'react'
import {
  Form,
  FormContent,
  FormMRData,
  FormStatusCode,
  getFormContent,
  getFormMRData,
} from '../types/Form'
import { useForm } from './useForm'
import { useFormContent, useFormContentReload } from './useFormContent'
import { useTrackUnsavedForm, useUntrackUnsavedForm } from './useUnsavedForms'

function formContentKey(formId: string) {
  return `form-content-${formId}`
}

function formContent(
  form: Form | undefined,
  payload: UserDataEntry | FormContent | null,
  mrData: FormMRData,
) {
  console.log('formContent -->', form, payload, mrData)

  if (!form || !mrData) {
    return
  }

  if (!payload) {
    return getFormContent(
      form,
      {
        response: {},
        signatures: {},
        signatureHistory: {},
        version: 0,
      },
      mrData,
    )
  }

  let content: FormContent

  if ('data' in payload) {
    content = FormContent.parse(JSON.parse(payload.data))
  } else {
    content = payload
  }

  return getFormContent(form, content, mrData)
}

function useStableForm(formId: string) {
  const { data: form } = useForm({ formId })

  const ref = useRef(form)

  if (
    !ref.current ||
    form?.id !== ref.current.id ||
    form?.version !== ref.current.version
  ) {
    ref.current = form
  }

  return ref.current
}

/**
 * Provides access to the draft for the content of the form with the given id.
 *
 * The draft stores local, unsaved, changes to the form's content.
 * If the user haven't made any changes to the form, the draft is synced to the server version and will reflect changes from other users after reloading content query.
 *
 * If changes has been made, the draft will remain unsynced until the user manually saves the form.
 *
 * @param formId The form id
 * @returns A [draft, setDraft] tuple
 */
export const useFormDraft = (formId: string) => {
  const { formId: currentFormId } = useAppContext()

  const isCurrentForm = formId === currentFormId

  const trackUnsaved = useTrackUnsavedForm()

  const mrAttrsByLocation = useMRAttributesByLocation()

  const form = useStableForm(formId)

  const { data: content = null } = useFormContent({
    formId,
    isEnabled: form?.statusCode !== FormStatusCode.NOT_STARTED,
  })
  const { data: record } = useUserData(formContentKey(formId))

  const register = useRegisterUserData()

  const setDraft = useCallback(
    async (value: FormContent) => {
      if (form) {
        await trackUnsaved(form)
      }
      await register({
        data: JSON.stringify(value),
        key: formContentKey(formId),
      })
    },
    [formId, form, register, trackUnsaved],
  )

  const mrData = useMemo(() => {
    if (form && mrAttrsByLocation && isCurrentForm) {
      return getFormMRData(form, mrAttrsByLocation)
    }

    return {}
  }, [form, mrAttrsByLocation, isCurrentForm])

  const draft = formContent(form, record ?? content, mrData)

  return [draft, setDraft] as const
}

/**
 * Resets the draft for the form with the given id.
 *
 * This will discard any local changes and resync the draft with the server version.
 *
 * @param formId The form id
 * @returns A function that resets the draft
 */
export const useResetFormDraft = () => {
  const remove = useRemoveUserData()
  const untrackUnsavedForm = useUntrackUnsavedForm()

  const { forceReload: reloadFormContent } = useFormContentReload()

  return async (formId: string) => {
    await reloadFormContent(formId)
    await remove(formContentKey(formId))
    await untrackUnsavedForm(formId)
  }
}

export { FormContent }
