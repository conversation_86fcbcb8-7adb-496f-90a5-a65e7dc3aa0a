import { getValue, Json, setValue } from '@/types/Json'
import { useI18n } from '@gatx-corp/platform-one-common'
import { useCallback, useMemo } from 'react'
import { ZodLiteral, ZodSchema } from 'zod'
import { useForm, useFormSignatures } from '../providers/FormProvider'
import {
  useFormQuestion,
  useFormQuestionResponse,
  useFormQuestionResponseOption,
  usePatternSchema,
} from '../providers/FormQuestionProvider'
import { GLOBAL_FORM_SIGNATURE_ID } from '../types/Form'
import { FormOptions, Option } from '../types/FormOptions'
import { Remove } from '../types/FormRemove'
import {
  getConstraints,
  getPropertyPossibleKeys,
  getPropertySchema,
} from '../utils/schema'
import { useFormErrors } from './useFormErrors'
import { useStableArray } from './useStableArray'

type FieldProps = {
  label: string
  value?: Json
  error?: string
  readOnly?: boolean
  onChange: (value: Json) => void
  min?: number
  max?: number
  step?: number
  options?: Option[]
  printOptions?: Option[]
  hidden: boolean
  multi?: boolean
}

function isDiscriminator(
  Schema: ZodSchema,
): Schema is FormOptions | ZodLiteral<string> {
  return [ZodLiteral, FormOptions].some((T) => Schema instanceof T)
}

type Options = {
  path?: string[]
  metadata?: Record<string, string>
  signable?: boolean
}

export function useFormFields({
  path: unstablePath = [],
  metadata = {},
  signable = false,
}: Options = {}): Partial<Record<string, FieldProps>> {
  const path = useStableArray(unstablePath)

  const form = useForm()
  const { t } = useI18n('Forms')

  const [response, setResponse] = useFormQuestionResponse()
  const [option] = useFormQuestionResponseOption()

  const question = useFormQuestion()

  const Schema = usePatternSchema()

  const values = useMemo(
    () => getValue(response, path) ?? ({} as Json),
    [response, path],
  )

  if (!values || typeof values !== 'object' || Array.isArray(values)) {
    throw new Error(
      `Cannot get field values for path ${path.join('.')}, property is not an object: ${JSON.stringify(values)}`,
    )
  }

  const properties = useMemo(() => {
    return getPropertyPossibleKeys(Schema, path)
  }, [Schema, path])

  const errors = useFormErrors({ path })

  const signatures = useFormSignatures()

  const readOnly =
    signable &&
    [path.join('.'), GLOBAL_FORM_SIGNATURE_ID, question.id.toString()].some(
      (s) => s && signatures.completed(s),
    )

  /**
   * Gets the localized string for the given path.
   * Tests different prefixes in order to find the localized string.
   *
   * @param path - The path to the property.
   * @param prefixes - The prefixes to the property.
   * @param fallback - The fallback value.
   * @returns The localized string.
   */
  const getLocalizedString = useCallback(
    <F>(
      path: string[],
      prefixes: (string | undefined)[],
      fallback: F,
    ): string | F => {
      /**
       * Ignore numeric keys in the middle of the path.
       */
      const key = path
        .filter((k, idx) => isNaN(Number(k)) || idx === path.length - 1)
        .join('.')

      const strings = prefixes.map((prefix) =>
        t([prefix, key].join('.'), { ...metadata, defaultValue: '' }),
      )

      return strings.find(Boolean) ?? fallback
    },
    [t, metadata],
  )

  return useMemo(
    () =>
      Object.fromEntries(
        properties.map((key) => {
          const [PropertySchema] = getPropertySchema(
            Schema,
            [...path, key],
            response,
          )

          /**
           * Transforms the option labels to the i18n keys.
           * Looks for field-specific definitions first. If none is found, looks into the common definitions.
           */
          function optionsToI18N(options: Option[] = []) {
            if (
              PropertySchema instanceof FormOptions &&
              PropertySchema._def.async
            ) {
              return options
            }

            return options.map((o) => ({
              label: getLocalizedString(
                [...path, key, o.label],
                [option?.pattern, form.template, 'common.options'],
                o.label,
              ),
              value: o.value,
              metadata: o.metadata,
            }))
          }

          /**
           * The blank print view needs the full set of options regardless of the form answers.
           */
          function getPrintOptions() {
            if (
              PropertySchema instanceof FormOptions &&
              !PropertySchema._def.printable
            ) {
              return []
            }

            const schemas = getPropertySchema(Schema, [...path, key])

            const discriminatorSchemas = schemas.filter((S) =>
              isDiscriminator(S),
            )

            const { options } = getConstraints(
              FormOptions.join(discriminatorSchemas),
            )

            return optionsToI18N(options as Option[])
          }

          const constraints = getConstraints(PropertySchema)

          const label = getLocalizedString(
            [...path, key],
            [option?.pattern, form.template, 'common'],
            key,
          )

          const help = getLocalizedString(
            [...path, key, 'help'],
            [option?.pattern, form.template],
            undefined,
          )

          const placeholder = getLocalizedString(
            [...path, key, 'placeholder'],
            [option?.pattern, form.template],
            undefined,
          )

          return [
            key,
            {
              ...constraints,
              options: optionsToI18N(constraints.options as Option[]),
              label: label,
              value: values[key],
              error: errors[key],
              readOnly,
              onChange(value: Json) {
                const next = setValue(response, path, {
                  ...values,
                  [key]: value,
                })
                setResponse(next).catch(console.error)
              },
              hidden: !PropertySchema || PropertySchema === Remove,
              /*
               * The print view needs the full set of options regardless of the form answers.
               */
              printOptions: getPrintOptions(),
              help,
              placeholder,
            },
          ]
        }),
      ),
    [
      Schema,
      path,
      response,
      option,
      form,
      values,
      errors,
      readOnly,
      properties,
      getLocalizedString,
      setResponse,
    ],
  )
}

export type { FieldProps }
