import { useI18n } from '@gatx-corp/platform-one-common'
import clsx from 'clsx'
import FormChoiceGroupField from '../../components/FormChoiceGroupField'
import FormTextField from '../../components/FormTextField'
import { FieldProps } from '../../hooks/useFormFields'
import { NO } from '../../types/FormSchemaConstants'

const IVPField = ({
  id,
  CorrectionComponent: CorrectionComponent,
  mrSuffix = '_MR',
  replySuffix = '_reply',
  fields,
  noSideEffects,
}: {
  id: string
  CorrectionComponent: React.ElementType
  mrSuffix?: string
  replySuffix?: string
  noSideEffects?: boolean
  fields: Partial<Record<string, FieldProps>>
}) => {
  mrSuffix = mrSuffix ?? '_MR'
  const { t } = useI18n('Forms')

  const mrField = fields[`${id}${mrSuffix}`]
  const replyField = fields[`${id}${replySuffix}`]
  const correctionField = fields[id]

  const shouldCorrect = fields.allCorrect?.value === NO
  const showReply = shouldCorrect && replyField

  return (
    <>
      <div
        className={clsx({
          'col-span-full': !showReply && correctionField?.hidden,
        })}
      >
        <FormTextField {...mrField!} readOnly />
      </div>

      {showReply && (
        <div
          className={clsx({
            'col-span-2': correctionField?.hidden,
          })}
        >
          <FormChoiceGroupField
            size="large"
            {...replyField}
            label={t('common.response')}
          />
        </div>
      )}

      {shouldCorrect && (
        <CorrectionComponent
          {...correctionField}
          label={t('common.correction')}
          noSideEffects={noSideEffects}
        />
      )}
    </>
  )
}

export { IVPField }
