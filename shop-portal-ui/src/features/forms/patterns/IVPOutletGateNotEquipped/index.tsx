import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import FormTextField from '../../components/FormTextField'

const IVPOutletGateNotEquipped = () => {
  const { t } = useI18n('Forms')
  // Access the first item in the array since the schema is defined as an array
  const fields = useFormFields({ path: ['0'], signable: true })

  console.log('fields -->', fields)

  const patternName = 'IVPOutletGateNotEquipped'
  return (
    <fieldset>
      <Alert
        compact
        level="info"
        title={t(`${patternName}.alertTitle`)}
        urgency="immediate"
      >
        {t(`${patternName}.alertDescription`)}
      </Alert>
      <div className="inline-grid grid-cols-[auto_auto_1fr] gap-md mt-md items-start">
        <div className="contents col-span-3">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.manufacturerMr_disp?.value
                  ? (fields.manufacturerMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.manufacturerMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
        <div className="contents col-span-3">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.modelMr_disp?.value
                  ? (fields.modelMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.modelMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
        <div className="contents col-span-3">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.typeMr_disp?.value
                  ? (fields.typeMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.typeMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
        <div className="contents col-span-3">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.materialMr_disp?.value
                  ? (fields.materialMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.materialMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
        <div className="contents col-span-3">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.connectionTypeMr_disp?.value
                  ? (fields.connectionTypeMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.connectionTypeMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
        <div className="contents">
          <div className="col-span-3">
            <FormTextField
              readOnly
              value={
                fields.locationMr_disp?.value
                  ? (fields.locationMr_disp.value as string)
                  : t('none')
              }
              label={t(`${patternName}.locationMr_disp`, {
                defaultValue: '',
              })}
              onChange={() => undefined}
            />
          </div>
        </div>
      </div>
    </fieldset>
  )
}

export default IVPOutletGateNotEquipped
