import { useFormFields } from '@/features/forms/hooks/useFormFields'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import FormTextField from '../../components/FormTextField'
import {
  useFormQuestionResponse,
  useResponseRows,
} from '../../providers/FormQuestionProvider'

const IVPOutletGateRow = ({ index }: { index: number }) => {
  const { t } = useI18n('Forms')
  const fields = useFormFields({ path: [index.toString()], signable: true })
  const [response] = useFormQuestionResponse()

  console.log('response --->', response)
  console.log('fields --->', fields)
  console.log(
    'item data --->',
    Array.isArray(response) ? response[index] : null,
  )
  return (
    <div className="flex flex-col gap-md">
      <Alert
        compact
        level="info"
        title={t('IVPOutletGateNotEquipped.alertTitle')}
        urgency="immediate"
        className="mt-md"
      >
        {t('IVPOutletGateNotEquipped.alertDescription')}
      </Alert>

      {[
        'manufacturer',
        'model',
        'type',
        'material',
        'connectionType',
        'location',
      ].map((field) => (
        <FormTextField
          key={field}
          {...fields[`${field}Mr_disp`]!}
          value={
            fields[`${field}Mr_disp`]?.value
              ? fields[`${field}Mr_disp`]?.value
              : t('none')
          }
          readOnly
        />
      ))}
      <hr className="border-light-7 dark:border-dark-5 col-span-full" />
    </div>
  )
}

const IVPOutletGateNotEquipped = () => {
  const rows = useResponseRows()

  return (
    <fieldset className="mt-md">
      {rows.array.map((_, index) => (
        <IVPOutletGateRow key={index} index={index} />
      ))}
    </fieldset>
  )
}

export default IVPOutletGateNotEquipped
