import { getOptions } from '@/features/forms/api/getOptions'
import { merge, mergeAll } from '@/features/forms/types/FormBranch'
import options from '@/features/forms/types/FormOptions'
import { z } from 'zod'
import { allCorrect, mr, reply } from '../../types/MRData'

const ManufacturerOptions = options().async((query) =>
  getOptions({ name: 'Manufacturer', ...query }),
)
const ModelOptions = options().async((query) =>
  getOptions({ name: 'ManufacturerModelNo', ...query }),
)
const TypeOptions = options().async((query) =>
  getOptions({ name: 'IVPOutletGateType', ...query }),
)
const MaterialOptions = options().async((query) =>
  getOptions({ name: 'IVPOutletGateMaterial', ...query }),
)
const ConnectionTypeOptions = options().async((query) =>
  getOptions({ name: 'IVPOutletGateConnectionType', ...query }),
)
const LocationOptions = options().async((query) =>
  getOptions({ name: 'IVPOutletGateLocation', ...query }),
)

const WithManufacturerReply = reply({
  name: 'manufacturer',
  change: ManufacturerOptions,
})
const WithModelReply = reply({
  name: 'model',
  change: ModelOptions,
})
const WithTypeReply = reply({
  name: 'type',
  change: TypeOptions,
})
const WithMaterialReply = reply({
  name: 'material',
  change: MaterialOptions,
})
const WithConnectionTypeReply = reply({
  name: 'connectionType',
  change: ConnectionTypeOptions,
})
const WithLocationReply = reply({
  name: 'location',
  change: LocationOptions,
})

const IVPOutletGateNotEquipped = mr({
  withData: merge(
    z.object({
      manufacturerMr_disp: z.string().default(''),
      modelMr_disp: z.string().default(''),
      typeMr_disp: z.string().default(''),
      materialMr_disp: z.string().default(''),
      connectionTypeMr_disp: z.string().default(''),
      locationMr_disp: z.string().default(''),
    }),
    allCorrect(
      mergeAll(
        WithManufacturerReply,
        WithModelReply,
        WithTypeReply,
        WithMaterialReply,
        WithConnectionTypeReply,
        WithLocationReply,
      ),
    ),
  ),
  withoutData: {
    manufacturer: ManufacturerOptions,
    model: ModelOptions,
    type: TypeOptions,
    material: MaterialOptions,
    connectionType: ConnectionTypeOptions,
    location: LocationOptions,
  },
}).array()

type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
