import { z } from 'zod'

const IVPOutletGateNotEquipped = z
  .object({
    manufacturerMr_disp: z.string().optional(),
    modelMr_disp: z.string().optional(),
    typeMr_disp: z.string().optional(),
    materialMr_disp: z.string().optional(),
    connectionTypeMr_disp: z.string().optional(),
    locationMr_disp: z.string().optional(),
    allCorrect: z.boolean().optional(),
    hasMRData: z.boolean().optional(),
    manufacturer: z.string().optional(),
    model: z.string().optional(),
    type: z.string().optional(),
    material: z.string().optional(),
    connectionType: z.string().optional(),
    location: z.string().optional(),
  })
  .array()

type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
