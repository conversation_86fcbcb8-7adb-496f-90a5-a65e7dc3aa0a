import { z } from 'zod'

const IVPOutletGateNotEquipped = z
  .object({
    manufacturerMr_disp: z.string().default(''),
    modelMr_disp: z.string().default(''),
    typeMr_disp: z.string().default(''),
    materialMr_disp: z.string().default(''),
    connectionTypeMr_disp: z.string().default(''),
    locationMr_disp: z.string().default(''),
  })
  .array()

type IVPOutletGateNotEquipped = z.infer<typeof IVPOutletGateNotEquipped>

export { IVPOutletGateNotEquipped }
