import { getOptions } from '@/features/forms/api/getOptions'
import branch, { merge, mergeAll } from '@/features/forms/types/FormBranch'
import options, {
  CantTell,
  Change,
  FormOptions,
  No,
  OK,
  Yes,
} from '@/features/forms/types/FormOptions'
import { IS_NEW_ROW } from '@/features/forms/types/FormSchemaConstants'
import { z } from 'zod'
import { mr, reply } from '../../types/MRData'

const Manufacturer = options().async((query) =>
  getOptions({ name: 'Manufacturer', ...query }),
)

const Model = options()
  .async(() => getOptions({ name: 'ManufacturerModelNo' }))
  .nonprintable()

const Shape = options()
  .async(() => getOptions({ name: 'Shape' }))
  .nonprintable()

const NominalSize = options()
  .async(() => getOptions({ name: 'ModelSize' }))
  .nonprintable()

const Style = options()
  .async(() => getOptions({ name: 'ValveType' }))
  .nonprintable()

const Material = options()
  .async(() => getOptions({ name: 'Material' }))
  .nonprintable()

const Location = options()
  .async(() => getOptions({ name: 'PartLocation' }))
  .nonprintable()

const WithLocationReply = reply({
  name: 'location',
  change: Location,
  suffix: 'Reply',
  withOk: true,
})

const WithMaterialReply = ({ withOk }: { withOk: boolean }) =>
  reply({
    name: 'material',
    change: Material,
    suffix: 'Reply',
    withOk,
  })

const WithStyleReply = ({ withOk }: { withOk: boolean }) =>
  reply({
    name: 'style',
    change: Style,
    suffix: 'Reply',
    withOk,
  })

const WithNominalSizeReply = ({ withOk }: { withOk: boolean }) =>
  reply({
    name: 'nominalSize',
    change: NominalSize,
    suffix: 'Reply',
    withOk,
  })

const WithShapeReply = ({ withOk }: { withOk: boolean }) =>
  reply({
    name: 'shape',
    change: Shape,
    suffix: 'Reply',
    withOk,
  })

const WithModelReply = (modelReply: FormOptions) =>
  branch(
    // @ts-expect-error FIXME: should not be needed once mergeAll does good type inference
    ['modelReply'],
    merge(
      z.object({
        modelReply: modelReply,
      }),
      mergeAll(
        WithShapeReply({ withOk: true }),
        WithNominalSizeReply({ withOk: true }),
        WithStyleReply({ withOk: true }),
        WithMaterialReply({ withOk: true }),
      ),
    ),
    merge(
      z.object({
        modelReply: Change,
        model: Model,
      }),
      mergeAll(
        WithShapeReply({ withOk: false }),
        WithNominalSizeReply({ withOk: false }),
        WithStyleReply({ withOk: false }),
        WithMaterialReply({ withOk: false }),
      ),
    ),
  )

const WithManufacturerReply = branch(
  // @ts-expect-error FIXME: should not be needed once mergeAll does good type inference
  ['manufacturerReply'],
  merge(
    z.object({
      manufacturerReply: OK.$or(CantTell),
    }),
    WithModelReply(OK.$or(CantTell)),
  ),
  merge(
    z.object({
      manufacturerReply: Change,
      manufacturer: Manufacturer,
    }),
    WithModelReply(CantTell),
  ),
)

const IVPHatches = mr({
  withData: merge(
    z.object({
      manufacturerMr_disp: z.string().default(''),
      modelMr_disp: z.string().default(''),
      shapeMr_disp: z.string().default(''),
      nominalSizeMr_disp: z.string().default(''),
      styleMr_disp: z.string().default(''),
      materialMr_disp: z.string().default(''),
      locationMr_disp: z.string().default(''),
      [IS_NEW_ROW]: z.boolean().default(false),
    }),
    branch(
      ['allCorrect'],
      z.object({
        allCorrect: Yes.$or(options('Not Equipped')),
      }),
      merge(
        z.object({
          allCorrect: No,
        }),
        mergeAll(WithManufacturerReply, WithLocationReply),
      ),
    ),
  ),
  withoutData: {
    manufacturer: Manufacturer,
    model: Model,
    shape: Shape,
    nominalSize: NominalSize,
    style: Style,
    material: Material,
    location: Location,
  },
}).array()

export { IVPHatches }
