import { useAuthorizedAction } from '@/app/hooks/useAuthorizedAction'
import { useIsOnline } from '@/features/connectivity/hooks/useIsOnline'
import { useRailcar } from '@/features/railcars/hooks/useRailcar'
import { useServiceEvent } from '@/features/service-events/hooks/useServiceEvent'
import { useAppContext } from '@/providers/AppProvider'
import { useMRAttributesByLocation } from '@/providers/MRAttributesProvider'
import { MESSAGES } from '@/providers/ToastProvider'
import { useI18n } from '@gatx-corp/platform-one-common'
import { Alert } from '@gatx-corp/platform-one-common/components/Alert'
import { useShowToast } from '@gatx-corp/platform-one-common/components/Toast'
import { useEffect, useMemo, useRef, useState } from 'react'
import { ZodSchema } from 'zod'
import { signForm } from '../api'
import FormComponent from '../components/Form'
import FormFooter from '../components/FormFooter'
import { useFormReload } from '../hooks/useForm'
import { FormContent, useResetFormDraft } from '../hooks/useFormDraft'
import { useFormListReload } from '../hooks/useFormList'
import { useFormSave } from '../hooks/useFormSave'
import useReferenceDocuments from '../hooks/useReferenceDocuments'
import { useCurrentFormDraft } from '../providers/FormDraftProvider'
import { getFormMRData, getFormResponseSchema, type Form } from '../types/Form'
import { DataConflictModal, useNotifyDataConflict } from './DataConflictModal'
import FormLayout from './FormLayout'
import UnsavedFormsModal from './UnsavedFormsModal'

const RELEASE = process.env.NEXT_PUBLIC_CLIENT_VERSION ?? ''

type Props = {
  form: Form
}

function useFormSchema(form: Form) {
  const [FormSchema, setFormSchema] = useState<ZodSchema | null>(null)
  const previousForm = useRef(form)

  useEffect(() => {
    if (!FormSchema || previousForm.current.id !== form.id) {
      getFormResponseSchema(form)
        .then((Schema) => {
          previousForm.current = form
          setFormSchema(Schema)
        })
        .catch(console.error)
    }
  }, [form, FormSchema])

  return FormSchema
}

const EditableForm = ({ form }: Props) => {
  const { t } = useI18n(['Forms', 'CommonUI'])
  const { carNumber, serviceEventId } = useAppContext()
  const { data: railcar, isLoading: isLoadingRailcar } = useRailcar({
    carNumber,
  })
  const { data: serviceEvent, isLoading: isLoadingServiceEvent } =
    useServiceEvent({
      serviceEventId,
    })

  const referenceDocuments = useReferenceDocuments(form.id)

  const mrAttrsByLocation = useMRAttributesByLocation()

  const [draft, setDraft] = useCurrentFormDraft()

  const FormSchema = useFormSchema(form)
  const isOnline = useIsOnline()

  const resetFormDraft = useResetFormDraft()
  const { forceReload: reloadForm } = useFormReload()
  const { forceReload: reloadFormList } = useFormListReload()

  const notifyConflict = useNotifyDataConflict()

  const { formId = '' } = useAppContext()
  const save = useFormSave(formId)
  const showToast = useShowToast()

  const authorizedSign = useAuthorizedAction(signForm)

  async function handleSign(signedContent: FormContent) {
    if (!isOnline) {
      await setDraft(signedContent)
      return
    }

    showToast(MESSAGES.saving({ level: 'info' }))

    const res = await save(signedContent)

    if (res.success) {
      showToast(MESSAGES.saving({ level: 'success' }))
    } else if (res.error.type !== 'concurrency-conflict') {
      showToast(MESSAGES.saving({ level: 'error' }))
      await setDraft(signedContent)
    }
  }

  async function handleComplete({
    content,
    html,
  }: {
    content: FormContent
    html: string
  }) {
    if (!form) return

    const res = await authorizedSign(form, {
      payload: {
        clientVersion: process.env.NEXT_PUBLIC_CLIENT_VERSION!,
        content,
        html,
      },
      version: form.version,
    })

    if (res.success) {
      if (res.response.formVersion !== form.version) {
        await reloadForm(formId)
        await reloadFormList(serviceEventId)
      }

      if (res.response.formContentVersion !== content.version) {
        await resetFormDraft(formId)
      }
    } else {
      if (res.error.type === 'concurrency-conflict') {
        notifyConflict(form.id)
      } else {
        throw new Error(
          t(`CommonUI:errors.${res.error.type}`, {
            context: res.error.message,
            defaultValue: t('CommonUI:errors.unknown'),
          }),
        )
      }
    }
  }

  const mrData = useMemo(() => {
    if (mrAttrsByLocation) {
      return getFormMRData(form, mrAttrsByLocation)
    }

    return null
  }, [form, mrAttrsByLocation])

  return isLoadingRailcar ||
    isLoadingServiceEvent ||
    !FormSchema ||
    !mrData ||
    !draft ? (
    <div className="p-md">Loading Form...</div>
  ) : railcar && serviceEvent ? (
    <div className="flex flex-col">
      <DataConflictModal />
      <UnsavedFormsModal />
      <FormLayout form={form}>
        <FormComponent
          form={form}
          Schema={FormSchema}
          content={draft}
          railcar={railcar}
          serviceEvent={serviceEvent}
          mrData={mrData}
          onContentChange={setDraft}
          onComplete={handleComplete}
          onSign={handleSign}
          completeDisabled={!isOnline}
          referenceDocuments={referenceDocuments}
          completeDisabledAlert={
            <Alert
              urgency="deferred"
              title={t('connectionAlertTitle')}
              level="warning"
              compact
              className="mb-md"
            >
              {t('connectionAlertMessage')}
            </Alert>
          }
        />
      </FormLayout>
      {draft && <FormFooter content={draft} release={RELEASE} />}
    </div>
  ) : (
    <Alert level="error" urgency="inline" compact>
      {t('nameNotFound', { name: !railcar ? t('railcar') : t('serviceEvent') })}
    </Alert>
  )
}

export default EditableForm
