{"loadingForm": "Loading Form...", "formNotFound": "Form Not Found", "connectionAlertTitle": "Online connection required", "connectionAlertMessage": "connection to the server is required for the final signature of this form", "railcar": "Railcar", "serviceEvent": "Service Event", "nameNotFound": "{{name}} Not Found", "noContentAlert": "This form is <strong>{{status}}</strong> but does not have content. Please contact support.", "inProgress": "In Progress", "completed": "Completed", "response": "Response", "questionResponseHint": "If <strong>{{name}}</strong> is selected, please provide the following information:", "common": {"options": {"Yes": "Yes", "No": "No", "N/A": "N/A", "Can't Tell": "Can't Tell"}, "cancel": "Cancel", "required": "Required", "requiredError": "This field is required", "addRow": "Add Row", "removeRow": "Remove Row", "response": "Response", "allCorrect": "Is the following information correct?", "correction": "Correction", "inspectionMethod": "Inspection Method"}, "status": {"NW": "Not Started", "IP": "In Progress", "CM": "Completed", "IN": "Deactivated", "AR": "Archived"}, "SelectForm": "<title>Select a Form</title><description>Please select a form from the Form List</description>", "Modal": {"archiveForm": "Archive Form", "archiveFormModalDescription": "Are you sure you want to archive this form? This action cannot be undone. Once a form is archived it will be view-only and may not be un-archived.", "reasonForArchive": "Reason for Archive", "selectReason": "Select Reason", "deactivateForm": "Deactivate Form", "deactivateFormModalConfirmation": "Are you sure you want to deactivate this form?", "reasonForDeactivating": "Reason for deactivating", "requiredFields": "You must complete all required fields", "error": "Error", "download": "Download", "updateForm": "Update Form", "resetForm": "Reset to New", "resetFormModalConfirmation": "Are you sure you want to reset form to New status? This will delete all previously saved form responses.", "DataConflict": {"title": "Conflict Detected: Changes Not Saved", "title_sign": "Conflict Detected: Form Not Signed", "title_deactivate": "Conflict Detected: Form Not Deactivated", "title_reactivate": "Conflict Detected: Form Not Reactivated", "title_archive": "Conflict Detected: Form Not Archived", "title_unsign": "Conflict Detected: Form Not Unsigned", "description": "<p>Someone else {{action}} this form. Their updates were made before yours, so {{outcome}}.</p><p>To prevent losing your work, we recommend downloading or printing your updates.</p>", "action": "saved changes to", "action_FormStatus.IN": "deactivated", "action_FormStatus.AR": "archived", "action_FormStatus.CM": "signed", "outcome": "your data could not be saved", "outcome_sign": "the form could not be signed", "outcome_deactivate": "the form could not be deactivated", "outcome_reactivate": "the form could not be reactivated", "outcome_archive": "the form could not be archived", "outcome_unsign": "the form could not be unsigned"}, "UnsavedForms": {"title": "Unsaved Changes", "title_offline": "Remember to Save Your Changes", "title_error": "Saving Failed", "description": "You have unsaved changes to this inspection form. Would you like to save your work before leaving?", "description_offline": "You are about to leave the form while working offline. Any changes you've made are stored locally, so your work will not be lost when you leave this page. Please remember to save these changes once you're back online.", "description_error": "Your work couldn't be saved. Please try again. If the issue continues, contact your system administrator for assistance.", "action.secondary": "Discard Changes", "action.primary": "Save Form", "action.primary_offline": "Continue", "action.primary_error": "Retry", "success": "Saved - {{formShortName}} Saved"}}, "FormStatusBanner": {"title_IN": "Deactivated Form", "title_AR": "Archived Form", "message_IN": "Read-only format. This form has been deactivated and may not be edited.", "message_AR": "Read-only format. This form has been archived and may not be edited."}, "SidePanel": {"formList": "Form List", "allTab": "All", "completeTab": "Complete", "inProgressTab": "In Progress", "newTab": "New", "loadingFormList": "Loading Form List...", "serviceEventNotFound": "Service Event Not Found", "unsavedForms": "Unsaved Forms", "unsavedFormsListMessage": "* Please note: Changes to these forms have not yet been saved to the server. To avoid losing your changes please save these as soon as possible."}, "ActionMenu": {"actionsForFormName": "Actions for {{formName}}", "actions": {"archive": "Archive", "deactivate": "Deactivate", "reactivate": "Reactivate", "reset": "Reset to New"}, "archive": {"io": "IO", "carReleased": "Car released from service", "customerChange": "Customer change in requirement", "newAssignment": "New assigment"}, "deactivation": {"addedInError": "Added in Error", "carScrapped": "Car being scrapped", "shopInstructionsChanged": "Shopping instructions changed"}}, "Footer": {"release": "Release", "firstSaved": "First Saved", "lastModified": "Modified", "version": "Version", "unknown": "Unknown", "textValue": "<label>{{text}}</label>: <value>{{value}}</value>"}, "SignatureInput": {"labelByName": "{{label}} by {{name}}", "unsignLabel": "Unsign {{label}}", "confirmLabel": "Confirm {{label}}", "signedByName": "Signed by {{name}}", "nameBy": "{{name}} By", "dateSigned": "Date Signed", "signedSectionMessage": "This section must be unsigned before it can be edited. Only individuals who have signed or have SysAdmin privileges may unsign."}, "CleaningForm": {"cleaningConfirmation": "Cleaning Confirmation", "showCleaningSummary": "Show Cleaning Summary", "hideCleaningSummary": "Hide Cleaning Summary", "allFieldsRequired": "* All fields are required unless specified otherwise"}, "Cleaning Confirmation": {"subtitle": "Cleaning Confirmation Sections", "procedureTBD": "Procedure TBD", "cleaningInspection": {"measurement": "Measurement", "measurement.inches": "Inches", "measurement.film": "Film", "measurement.N/A": "N/A", "depthOfCommodity": "Depth of Commodity (Inches)", "totalGallons": "Total Gallons", "solidsPresent": "Solids Present", "pH": "pH", "ventOnly": "Vent Only", "exteriorCleanRequired": "Exterior Cleaning", "pressure": "Pressure", "temperature": "Car Temperature (F)", "ambientTemperature": "Ambient Temp at Time of Estimate", "HPInCar": "HP Chart indicates Liquid in Car", "QGtest": "Quantitative Gas Testing", "QGtest.pass": "Pass", "QGtest.fail": "Fail", "QGtest.not-tested": "Not Tested", "QGTtubeUsed": "Tube Used", "QGTppm": "PPM", "title": "Inspection Information", "signature": {"label": "Inspection", "acknowledgment": "I acknowledge that I have inspected this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}}, "cleaningApproval": {"arrivedClean": "Car Arrived Clean", "confirmed": "Approved for Cleaning", "confirmed.approved": "Approved", "confirmed.rejected": "Rejected", "confirmed.not-required": "Not Required", "approvalDetails": "Approval Details", "approvalDetails.Per EGP-05 Guideline": "Per EGP-05 Guideline", "approvalDetails.Per Customer": "Per Customer", "rejectionDetails": "Rejection Details", "rejectionDetails.Handle DNO/DNE": "Handle DNO/DNE", "rejectionDetails.Reject to Customer": "Reject to Customer", "title": "Approval For Cleaning/Purge", "signature": {"label": "Approval Decision", "acknowledgment": "I acknowledge that I have reviewed the inspection information to determine this car's cleaning approval. Any future changes will require my response to be unsigned by myself or an admin."}}, "cleaningInformation": {"cleaningSpecType": "Review Status", "cleaningSpecType.reviewed": "I have reviewed the customer specification for cleaning", "cleaningSpecType.none": "This car has no customer specifications for cleaning", "cleanedPer": "Cleaned Per", "cleanedPer.gatx_recipe": "Recipe {{recipe<PERSON>umber}}", "cleanedPer.customer_spec": "Customer Spec", "cleaningMethods": "Additional Cleaning Methods", "cleaningPerformed": "Additional Cleaning Performed", "cleaningPerformed.Car is Clean - Vent Only": "Car is Clean - Vent Only", "cleaningPerformed.Minor Cleaning of Clean Car": "Minor Cleaning of Clean Car", "cleaningPerformed.Exterior Cleaning": "Exterior Cleaning", "cleaningPerformed.No Cleaning": "No Cleaning", "totalGalRemoved": "Total Gallons Removed", "drumLocation": "Waste Location", "totalFlareHours": "Total Flare Time (Hours)", "entryProceduresMessageParagraph1": "* All required confined space entry procedures must be followed to determine if the railcar is deemed safe for human entry", "entryProceduresMessageParagraph2": "* Select all that apply", "cleaningMethodsLabel": "Cleaning Methods", "title": "Cleaning Information", "signature": {"label": "Cleaning Performed", "acknowledgment": "I acknowledge that I have cleaned this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}}, "cleaningQA": {"LEL": "LEL (%)", "interiorInspectionPass": "Interior Inspection Pass", "exteriorInspectionPass": "Exterior Inspection Pass", "placardsRemoved": "Placards Removed", "commodityStencilCovered": "Commodity Stencil Covered", "orangeTagApplied": "Orange Cleaning Tag Applied", "NiApplied": "Nitrogen Warning Tag Applied", "pleaseNote": "Please note — ", "niTagNotExpected": "Recipe does not indicate Nitrogen was used. Nitrogen Warning Tag not expected", "niTagExpected": "Recipe indicates Nitrogen was used. Nitrogen Warning Tag expected unless car has been vented with air", "niMismatch": "Please Note: This field does not match Cleaning Information", "title": "Quality Assurance", "signature": {"label": "Cleaning Verified", "acknowledgment": "I acknowledge that I have verified the quality of this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}}}, "CleaningSummary": {"carNumber": "Car Number", "facility": "Facility", "capacity": "Capacity", "customer": "Customer", "commodity": "Commodity", "confirmedCIN": "Confirmed CIN#", "stcc": "STCC#", "restrictionCode": "Restriction Code", "sds": "SDS", "carReportedClean": "Car Reported Clean", "dno": "DNO", "dne": "DNE", "rc": "RC"}, "QuantitativeGasTestingReference": {"showReference": "Show Quantitative Gas Testing Reference", "hideReference": "Hide Quantitative Gas Testing Reference", "heading": "TLV (PPM) by Chemical"}, "Inbound Inspection": {"title": "Inbound Visual Inspection", "sectionsTitle": "Inbound Visual Inspection Sections", "inspected": "Inspected", "inspectedComplete": "Complete", "inspectedDeferred": "Deferred", "action": {"complete": "completed", "deferred": "deferred", "unsign": "unsigned"}, "updateInspection": "Update", "inspectionMethod.Visual": "NDT Method Visual", "procedure": "Procedure", "itemCompletedOrDeferredBy": "This item has been {{action}} by {{name}} on {{dateString}}", "signature": {"label": "Completed", "acknowledgment": "I acknowledge that I have inspected this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}, "toggle": {"zones": "Show More Zones", "zones_expanded": "Show Less Zones"}, "filter": {"label": "Filter by", "empty": "<title>No filtered inspection items.</title><description>There are no inspection items in this zone that meet the selected criteria.</description>", "option.all": "All", "option.defects": "Defects", "option.incomplete": "Incomplete", "option.IVPs": "IVPs"}, "Inbd Visual Zone 1 (L)": "Inbound Visual Inspection Zone 1 (L)", "Inbd Visual Zone 1 (L)_group": "Zone 1 (L) Inspection Items", "Inbd Visual Zone 1 (L)_nav": "Zone 1 (L)", "Inbd Visual Zone 2 (BL)": "Inbound Visual Inspection Zone 2 (BL)", "Inbd Visual Zone 2 (BL)_group": "Zone 2 (BL) Inspection Items", "Inbd Visual Zone 2 (BL)_nav": "Zone 2 (BL)", "Inbd Visual Zone 3 (B)": "Inbound Visual Inspection Zone 3 (B)", "Inbd Visual Zone 3 (B)_group": "Zone 3 (B) Inspection Items", "Inbd Visual Zone 3 (B)_nav": "Zone 3 (B)", "Inbd Visual Zone 4 (BR)": "Inbound Visual Inspection Zone 4 (BR)", "Inbd Visual Zone 4 (BR)_group": "Zone 4 (BR) Inspection Items", "Inbd Visual Zone 4 (BR)_nav": "Zone 4 (BR)", "Inbd Visual Zone 5 (R)": "Inbound Visual Inspection Zone 5 (R)", "Inbd Visual Zone 5 (R)_group": "Zone 5 (R) Inspection Items", "Inbd Visual Zone 5 (R)_nav": "Zone 5 (R)", "Inbd Visual Zone 6 (AR)": "Inbound Visual Inspection Zone 6 (AR)", "Inbd Visual Zone 6 (AR)_group": "Zone 6 (AR) Inspection Items", "Inbd Visual Zone 6 (AR)_nav": "Zone 6 (AR)", "Inbd Visual Zone 7 (A)": "Inbound Visual Inspection Zone 7 (A)", "Inbd Visual Zone 7 (A)_group": "Zone 7 (A) Inspection Items", "Inbd Visual Zone 7 (A)_nav": "Zone 7 (A)", "Inbd Visual Zone 8 (AL)": "Inbound Visual Inspection Zone 8 (AL)", "Inbd Visual Zone 8 (AL)_group": "Zone 8 (AL) Inspection Items", "Inbd Visual Zone 8 (AL)_nav": "Zone 8 (AL)", "Inbd Visual Zone 9 (CB)": "Inbound Visual Inspection Zone 9 (CB)", "Inbd Visual Zone 9 (CB)_group": "Zone 9 (CB) Inspection Items", "Inbd Visual Zone 9 (CB)_nav": "Zone 9 (CB)", "Inbd Visual Zone 10 (CT)": "Inbound Visual Inspection Zone 10 (CT)", "Inbd Visual Zone 10 (CT)_group": "Zone 10 (CT) Inspection Items", "Inbd Visual Zone 10 (CT)_nav": "Zone 10 (CT)"}, "Inbd Exterior Coating Insp": {"InspectionItem": "Inspection Item", "title": "Exterior Coating", "signature": {"label": "Approval Decision", "acknowledgment": "I acknowledge that I have inspected this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}}, "General": {"subtitle": "Please provide additional information about the failure / defective condition:", "inspectionPoint": "Inspection Point", "inspectionPointComment": "Comments", "jobCode": "Job Code", "qualifier": "Qualifier", "whyMade": "Why Made", "whyMade.help": "Why Made code 33 (Derailment / Accident Damage) must be used before any other applicable why made code.", "quantity": "Quantity", "quantity.help": "Value must be a positive number between 0 and 999, up to 2 characters allowed after decimal point.\n\n<PERSON><PERSON><PERSON> JOB CODE IS TACK/FILLET (4800) OR GROOVE WELD (4808) AND WHY MADE IS CRACKED (41) THEN INPUT LENGTH OF CRACK FOR QUANTITY.  FOR <PERSON><PERSON><PERSON><PERSON><PERSON> CRACKS ADD A NEW ROW. NOTE FOLLOWING:  ROUND LENGTH OF WELD TO NEAREST 1/2 INCH.\n\nFOR STACK/WELL, SPINE, OR ABC CARS DESIGNATE UNIT AS FOLLOWS 100 FOR B, 200 FOR C, 300 FOR D, 400 FOR E, AND 500 FOR A. EXAMPLES: B-<PERSON><PERSON> CRACK OF 13.5 INCHES WOULD BE ENTERED AS 113.5 IN THE QUANTITY FIELD. FOR OTHER B-UNIT DEFECTS (NON-CRACK) ENTER 100 FOR QUANTITY TO DESIGNATE B-UNIT."}, "FM Affirmation": {"inspectionItem": "Inspection Item", "title_1": "FM Affirmation Part 01", "title_2": "FM Affirmation Part 02", "title": "FM Affirmation", "signature": {"label": "Affirmation", "acknowledgment": "I acknowledge that I have inspected this car and filled out the above information correctly at this time. Any future changes will require my response to be unsigned by myself or an admin."}}, "Decal": {"subtitle": "Please provide additional information about the failure / defective condition:", "inspectionPoint": "Inspection Point", "inspectionPointComment": "Comments", "jobCode": "Job Code", "qualifier": "Qualifier", "whyMade": "Why Made", "whyMade.help": "Why Made code 33 (Derailment / Accident Damage) must be used before any other applicable why made code."}, "WhyMade": {"subtitle": "Please provide additional information about the failure / defective condition:", "comments": "Comments", "comments.placeholder": "Please enter comment", "whyMade": "Why Made", "whyMade.help": "Why Made code 33 (Derailment / Accident Damage) must be used before any other applicable why made code."}, "IVPHatches": {"title": "IVP Hatches", "addData": "Add Data", "manufacturer": "Manufacturer", "manufacturerMr_disp": "Manufacturer", "model": "Manufacturer Model", "modelMr_disp": "Manufacturer Model", "shape": "<PERSON><PERSON><PERSON>", "shapeMr_disp": "<PERSON><PERSON><PERSON>", "nominalSize": "Nom Dia/Width", "nominalSizeMr_disp": "Nom Dia/Width", "style": "Style", "styleMr_disp": "Style", "material": "Material", "materialMr_disp": "Material", "location": "The Hatch's Location", "locationMr_disp": "The Hatch's Location"}, "ProgConfAffirm": {"programDetail": "Program Detail"}, "IVPCommonEquipped": {"title": "IVP Common Equipped", "currentData_MR": "Current Data"}, "IVPCommonEquippedText": {"currentData_MR": "Current Data", "currentData_reply": "Response", "currentData.placeholder": "Enter correct data", "currentData_MR.placeholder": "Enter data"}, "IVPLightweight": {"title": "IVP Lightweight", "lightWeight.placeholder": "Enter lightweight", "lightWeight_MR": "Lightweight", "lightWeight_reply": "Response", "lightWeightDate.placeholder": "Enter lightweight date", "lightWeightDate_MR": "Lightweight Date", "lightWeightDate_reply": "Response", "lightWeightBy.placeholder": "Enter lightweight by", "lightWeightBy_MR": "Lightweight By", "lightWeightBy_reply": "Response"}, "IVPCommonNotEquipped": {"title": "Item Not Equipped", "itemWillBeRemoved": "item will be removed.", "currentData_MR": "Current Data"}, "IVPTruck": {"truckType_MR": "Type", "brakeSystem_MR": "Brake System Type", "springGrouping_MR": "Spring Grouping"}, "noMRData": "No MR Data available", "noMRDataText": "please provide data.", "none": "None", "checks": {"Banner.title": "Automated Comment", "Banner.notice": "This comment will be included in the defect report.", "DecalPresent": "Commodity Decal Present - check lease status, customer spec, OSS and regulatory requirements, as applicable, to determine if Commodity Decal should be removed.", "DecalNotPresent": "Check lease status, customer spec, OSS and regulatory requirements, as applicable, to determine if a Commodity Decal is required.", "CastingOutdated": "Casting out-of-date."}, "IVPBuilderLot": {"information": "IVP Builder Lot Information", "title": "IVP Builder Lot", "allCorrect": "Is the following information correct?", "allCorrect.help": "Typically found near body bolster webs at BL or AL sides", "builderName_MR": "Builder Name", "builderName.placeholder": "Enter Builder Name", "lotNumber_MR": "Lot Number", "lotNumber.placeholder": "Enter Lot Number"}, "IVPExteriorPaint": {"title": "IVP Builder Lot", "yearPainted_MR": "Year Painted", "paintedBy_MR": "Painted By", "paintSystem_MR": "Paint System", "paintColor_MR": "Paint Color", "paintedBy.placeholder": "Enter Painted By"}, "IVPOutletGateNotEquipped": {"title": "IVP Outlet Gate Not Equipped", "manufacturerMr_disp": "Manufacturer", "modelMr_disp": "Manufacturer Model", "typeMr_disp": "Type", "materialMr_disp": "Material", "connectionTypeMr_disp": "Connection Type", "locationMr_disp": "The Gate's Location", "alertTitle": "Item Not Equipped", "alertDescription": "item will be removed."}, "IVPCastingDate": {"title": "IVP Casting Date", "castingMonth_MR": "Casting Month", "castingYear_MR": "Casting Year"}}