import { Railcar } from '@/features/railcars/types/Railcar'
import { ServiceEvent } from '@/features/service-events/types/ServiceEvent'
import {
  createContext,
  PropsWithChildren,
  ReactNode,
  SetStateAction,
  use,
  useCallback,
  useContext,
  useMemo,
} from 'react'
import { flushSync } from 'react-dom'
import { createRoot } from 'react-dom/client'
import { ZodSchema } from 'zod'
import FormComponent from '../components/Form'
import {
  createFormSignature,
  Form,
  FormMRData,
  FormQuestion,
  FormResponse,
  FormSignature,
  FormSignatureHistory,
  FormSignatures,
  FormSignatureType,
  getSelectedResponseOption,
} from '../types/Form'
import { getHTMLExport } from '../utils/export'

import { ReferenceDocument } from '@/features/documents/types/ReferenceDocument'
import { PLACEHOLDER_USER } from '@/types/permissions'
import { formatDate } from '@/utils/dates'
import { PlatformOneProvider } from '@gatx-corp/platform-one-common'
import { type User, useUser } from '@gatx-corp/platform-one-common/auth'
import { AccordionHandle as QuestionHandle } from '@gatx-corp/platform-one-common/components/Accordion'
import { FormContent } from '../hooks/useFormDraft'
import { useFormSignables } from '../hooks/useFormSignables'
import { invariant, StateDuple } from '../utils'
import { addSignatureHistoryEntry } from '../utils/formActions'
import { getPropertySchema } from '../utils/schema'

const FormSpecContext = createContext<Form | undefined>(undefined)
const FormSchemaContext = createContext<ZodSchema | undefined>(undefined)
const FormResponseContext = createContext<StateDuple<FormResponse> | undefined>(
  undefined,
)
const SetFormContentContext = createContext<
  ((content: SetStateAction<FormContent>) => Promise<void>) | undefined
>(undefined)

const FormSignaturesContext = createContext<FormSignatures | undefined>(
  undefined,
)

const FormMRDataContext = createContext<FormMRData | undefined>(undefined)

type FormContext = {
  /**
   * Holds the signature history for this form.
   * The key is the id of a signable component of the form. For example, a form section or the form itself.
   * The value is an array of signature history entries tracking all sign/unsign actions
   */
  signatureHistory: FormSignatureHistory

  /**
   * Sets the signature history to the whole form
   *
   * @param signatureHistory the signature history of the form
   * @returns
   */
  setSignatureHistory: (
    action: SetStateAction<FormSignatureHistory>,
  ) => Promise<void>

  /**
   * Validates the Form against its schema.
   *
   * @returns
   */
  validate: () => boolean

  /**
   * The version of the form content.
   */
  version: number

  /**
   * The railcar associated with the form.
   */
  railcar: Railcar

  /**
   * The service event associated with the form.
   */
  serviceEvent: ServiceEvent

  /**
   * A function to be called when the form is completed successfully. Receives a exported representation of the form.
   */
  onComplete?: (exp: { content: FormContent; html: string }) => Promise<void>

  /**
   * Whether the form can be completed or not. If true, disables the signature input that triggers the completion of the form.
   * Other sections of the form may still be signable.
   */
  completeDisabled: boolean

  /**
   * An alert to be shown when the form completion is disabled.
   */
  completeDisabledAlert: ReactNode

  /**
   * A function to be called when the form is signed. Receives a signed representation of the form.
   */
  onSign?: (signedContent: FormContent) => Promise<void>

  /**
   * A set of question ids that are hidden from the form in screen media.
   */
  hiddenQuestionIds: Set<FormQuestion['id']>

  /**
   * The reference documents for the form.
   */
  referenceDocuments: ReferenceDocument[]

  /**
   * A reference handler for the questions accordion.
   */
  questionHandle?: React.RefObject<QuestionHandle | null>
}

function dispatch<T extends object>(action: SetStateAction<T>, current: T): T {
  if (typeof action === 'function') {
    return action(current)
  }

  return action
}

const FormContext = createContext<FormContext | undefined>(undefined)

type Props = PropsWithChildren<{
  form: Form
  railcar: Railcar
  serviceEvent: ServiceEvent
  mrData: FormMRData
  content: FormContent
  completeDisabled?: boolean
  completeDisabledAlert?: ReactNode
  hiddenQuestionIds?: Set<FormQuestion['id']>
  questionHandle?: React.RefObject<QuestionHandle | null>
  referenceDocuments: ReferenceDocument[]
  onContentChange?: (content: FormContent) => Promise<void>
  onComplete?: (exp: { content: FormContent; html: string }) => Promise<void>
  onSign?: (signedContent: FormContent) => Promise<void>
  Schema: ZodSchema
}>

const FormProvider = ({
  form,
  railcar,
  serviceEvent,
  content,
  mrData,
  children,
  onContentChange,
  onComplete,
  completeDisabled = false,
  completeDisabledAlert = false,
  hiddenQuestionIds = new Set(),
  questionHandle,
  onSign,
  Schema,
  referenceDocuments,
}: Props) => {
  const setContent = useCallback(
    async function (action: SetStateAction<FormContent>) {
      await onContentChange?.(dispatch(action, content))
    },
    [content, onContentChange],
  )

  const setResponse = useCallback(
    async function (action: SetStateAction<FormResponse>) {
      await onContentChange?.({
        ...content,
        response: dispatch(action, content.response),
      })
    },
    [content, onContentChange],
  )

  const setSignatureHistory = useCallback(
    async function (action: SetStateAction<FormSignatureHistory>) {
      await onContentChange?.({
        ...content,
        signatureHistory: dispatch(action, content.signatureHistory),
      })
    },
    [content, onContentChange],
  )

  const validate = useCallback(
    function () {
      return Schema.safeParse(content.response).success
    },
    [Schema, content],
  )

  const context = useMemo(
    () => ({
      form,
      signatures: content.signatures,
      signatureHistory: content.signatureHistory,
      version: content.version,
      setSignatureHistory,
      validate,
      railcar,
      serviceEvent,
      onComplete,
      completeDisabled,
      completeDisabledAlert,
      onSign,
      hiddenQuestionIds,
      referenceDocuments,
      questionHandle,
    }),
    [
      form,
      content,
      setSignatureHistory,
      validate,
      railcar,
      serviceEvent,
      onComplete,
      completeDisabled,
      completeDisabledAlert,
      onSign,
      hiddenQuestionIds,
      referenceDocuments,
      questionHandle,
    ],
  )

  const responseContext = useMemo(
    (): StateDuple<FormResponse> => [content.response, setResponse],
    [content.response, setResponse],
  )

  return (
    <div id="current-form">
      <FormSpecContext.Provider value={form}>
        <FormContext.Provider value={context}>
          <FormResponseContext.Provider value={responseContext}>
            <FormSchemaContext.Provider value={Schema}>
              <FormSignaturesContext.Provider value={content.signatures}>
                <SetFormContentContext.Provider value={setContent}>
                  <FormMRDataContext.Provider value={mrData}>
                    {children}
                  </FormMRDataContext.Provider>
                </SetFormContentContext.Provider>
              </FormSignaturesContext.Provider>
            </FormSchemaContext.Provider>
          </FormResponseContext.Provider>
        </FormContext.Provider>
      </FormSpecContext.Provider>
    </div>
  )
}

function useFormContext(): FormContext {
  const context = useContext(FormContext)

  if (!context) {
    throw new Error('useFormContext must be used within a FormProvider')
  }

  return context
}

type FormSignatureMapWith<Id extends string> = FormSignatureMap & {
  map: Record<string, FormSignature> & Record<Id, FormSignature>
}

type FormSignatureMap = {
  /**
   * Holds the actual signatures of the form.
   */
  map: Partial<Record<string, FormSignature>>
  /**
   * Takes the id of a signable component of the form and marks it as signed by the current user.
   * Examples of signable components are form sections or the form itself.
   *
   * @param signableId the id of the signable component of the form.
   * @param type type of this signature - either complete or deferred. This is optional, and is assumed "complete"
   */
  sign: (id: string, type?: FormSignatureType) => Promise<void>
  /**
   * Takes the id of a signable component of the form and marks it as unsigned by the current user.
   * Examples of signable components are form sections or the form itself.
   *
   * @param id the id of the signable component of the form.
   */
  unsign: (id: string) => Promise<void>
  /**
   * Takes the ID of a signable component of the form and returns the entire FormSignature data, or undefined
   * if there is no signature with the provided ID.
   *
   * @param id the id of the signable component of the form.
   * @returns the FormSignature for the provided ID or undefined if there is no signature with the ID.
   */
  get: (id: string) => FormSignature | undefined
  /**
   * Takes the id of a signable component of the form and returns whether it has been signed as complete. This
   * returns true if the component has been signed as complete, but will return false if it's deferred. Examples
   * of signable components are form sections or the form itself.
   *
   * @param signableId the id of the signable component of the form.
   * @returns true if the signable component has been signed, false if deferred or not signed.
   */
  completed: <Id extends string>(id: string) => this is FormSignatureMapWith<Id>
  /**
   * Takes the id of a signable component of the form and returns whether it has been deferred. This only
   * returns true if the component has been signed as deferred. Examples of signable components are form sections
   * or the form itself.
   *
   * @param signableId the id of the signable component of the form.
   * @returns true if the signable component has been deferred, false if signed or not signed.
   */
  deferred: <Id extends string>(id: string) => this is FormSignatureMapWith<Id>
  /**
   * Takes an id and returns whether it corresponds to a signable component of the form.
   * Examples of signable components are form sections or the form itself.
   *
   * @param signableId the id of the signable component of the form.
   * @returns true if the signable component is signable, false if not.
   */
  signable: (id: string) => boolean
  /**
   * Returns the ids of all signable components of the form.
   * Examples of signable components are form sections or the form itself.
   *
   * @returns the ids of all signable components of the form.
   */
  signables: string[]
}

function sign(
  id: string,
  user: User,
  type: FormSignatureType,
  signatures: FormSignatures,
): FormSignatures {
  return {
    ...signatures,
    [id]: createFormSignature(id, user, type),
  }
}

function useFormSignatures(): FormSignatureMap {
  const user = useUser()
  const setContent = useSetFormContent()
  const signatures = use(FormSignaturesContext)

  invariant(signatures, 'Must be used within a provider')

  const signables = useFormSignables()

  return {
    map: signatures,
    async sign(id: string, type?: FormSignatureType) {
      if (!user) {
        throw new Error('Cannot sign form: user not available')
      }

      const signatureType = type ?? 'complete'

      await setContent((content) => ({
        ...content,
        signatures: sign(id, user, signatureType, content.signatures),
        signatureHistory: addSignatureHistoryEntry(
          id,
          user,
          signatureType,
          content.signatureHistory,
        ),
      }))
    },
    async unsign(id: string) {
      if (!user) {
        throw new Error('Cannot unsign form: user not available')
      }

      await setContent((content) => {
        const updatedSignatures = { ...content.signatures }
        delete updatedSignatures[id]
        const updatedSignatureHistory = addSignatureHistoryEntry(
          id,
          user,
          'unsign',
          content.signatureHistory,
        )

        return {
          ...content,
          signatures: updatedSignatures,
          signatureHistory: updatedSignatureHistory,
        }
      })
    },
    get(id: string): FormSignature | undefined {
      return signatures[id]
    },
    completed<Id extends string>(id: Id): this is FormSignatureMapWith<Id> {
      return id in signatures && signatures[id]?.type === 'complete'
    },
    deferred<Id extends string>(id: Id): this is FormSignatureMapWith<Id> {
      return id in signatures && signatures[id]?.type === 'deferred'
    },
    signable(id: string): boolean {
      return signables.includes(id)
    },
    signables,
  }
}

function useFormResponse() {
  const context = use(FormResponseContext)

  invariant(context, 'Must be used within a provider')

  return context
}

function useForm(): Form {
  const form = use(FormSpecContext)

  invariant(form, 'Must be used within a provider')

  return form
}

function useFormSchema(): ZodSchema {
  const context = use(FormSchemaContext)

  invariant(context, 'Must be used within a provider')

  return context
}

function useSetFormContent() {
  const context = use(SetFormContentContext)

  invariant(context, 'Must be used within a provider')

  return context
}

function useValidateForm(): () => boolean {
  return useFormContext().validate
}

function useSignFormSection(): {
  signForm: (sectionId: string) => Promise<void>
} {
  const { onSign, version, signatureHistory } = useFormContext()
  const [response] = useFormResponse()
  const signatures = useFormSignatures()

  const user = useUser()

  async function signForm(sectionId: string, type?: FormSignatureType) {
    if (!user) {
      throw new Error('Cannot sign form: user not available')
    }

    const signatureType = type ?? 'complete'

    const signedContent = {
      response,
      signatures: sign(sectionId, user, signatureType, signatures.map),
      signatureHistory: addSignatureHistoryEntry(
        sectionId,
        user,
        signatureType,
        signatureHistory,
      ),
      version,
    }
    await onSign?.(signedContent)
  }

  return { signForm }
}

function useCompleteForm(): {
  complete: (finalSectionId: string) => Promise<void>
  disabled: boolean
  alert?: ReactNode
} {
  const railcar = useFormRailcar()
  const serviceEvent = useFormServiceEvent()
  const form = useForm()
  const [response] = useFormResponse()
  const Schema = useFormSchema()
  const mrData = useFormMRData()

  const {
    version,
    signatureHistory,
    onComplete,
    completeDisabled,
    completeDisabledAlert,
    referenceDocuments,
  } = useFormContext()

  const signatures = useFormSignatures()
  const user = useUser()

  async function complete(finalSectionId: string) {
    if (!user) {
      throw new Error('Cannot sign form: user not available')
    }

    const signedContent = {
      response,
      signatures: sign(finalSectionId, user, 'complete', signatures.map),
      signatureHistory: addSignatureHistoryEntry(
        finalSectionId,
        user,
        'complete',
        signatureHistory,
      ),
      version,
    }

    const html = getFormHTMLExport({
      form,
      railcar,
      serviceEvent,
      content: signedContent,
      Schema,
      referenceDocuments,
      mrData,
    })

    await onComplete?.({ content: signedContent, html })
  }

  return {
    complete,
    disabled: completeDisabled,
    alert: completeDisabledAlert,
  }
}

function useFormRailcar(): Railcar {
  return useFormContext().railcar
}

function useFormServiceEvent(): ServiceEvent {
  return useFormContext().serviceEvent
}

function getFormHTMLExport(params: {
  form: Form
  railcar: Railcar
  serviceEvent: ServiceEvent
  content: FormContent
  Schema: ZodSchema
  referenceDocuments: ReferenceDocument[]
  mrData: FormMRData
}): string {
  const container = document.createElement('div')
  const root = createRoot(container)

  flushSync(() =>
    root.render(
      <PlatformOneProvider
        application="PLATFORM_ONE_SHOP_PORTAL"
        defaultUser={PLACEHOLDER_USER}
        disableAuth
      >
        <FormComponent {...params} />
      </PlatformOneProvider>,
    ),
  )

  if (!container.innerHTML) {
    throw Error('Could not export form.')
  }

  const currentForm = container.querySelector('#current-form')!

  if (!currentForm) {
    throw Error('Form component not found')
  }

  const serviceEventSummary = params.railcar.serviceEvents.find(
    ({ id }) => id === params.serviceEvent.serviceEventId,
  )

  if (!serviceEventSummary) {
    throw new Error('Service event not found. Please try again later.')
  }

  const title = `${params.railcar.carNumber} - ${params.form.shortName}`

  return getHTMLExport(currentForm as HTMLElement, {
    title,
    attributes: {
      'data-form-title': title,
      'data-form-railcar': `${params.railcar.carNumber} ${serviceEventSummary.shop} IB: ${formatDate(serviceEventSummary.inboundDate)}`,
    },
  })
}

function useHiddenQuestionIds(): Set<FormQuestion['id']> {
  return useFormContext().hiddenQuestionIds
}

function useQuestionHandle():
  | React.RefObject<QuestionHandle | null>
  | undefined {
  return useFormContext().questionHandle
}

function getFormQuestionPatternSchema(
  question: FormQuestion,
  response: FormContent['response'],
  Schema: ZodSchema,
): ZodSchema {
  const option = getSelectedResponseOption(question, response)

  const questionId = question.id.toString()

  const [PatternSchema] = getPropertySchema(Schema, [questionId, 'data'], {
    [questionId]: { responseId: option?.id },
  })

  return PatternSchema
}

function useFormQuestionPatternSchema(question: FormQuestion): ZodSchema {
  const Schema = useFormSchema()
  const [response] = useFormResponse()

  return getFormQuestionPatternSchema(question, response, Schema)
}

function getFormQuestionStatus(
  question: FormQuestion,
  response: FormContent['response'],
  Schema: ZodSchema,
) {
  if (!response[question.id].responseId || !Schema) {
    return 'not-started'
  }

  if (Schema.safeParse(response[question.id].data).success) {
    return 'valid'
  }

  return 'invalid'
}

function useGetFormQuestionStatus() {
  const [currentResponse] = useFormResponse()
  const Schema = useFormSchema()

  return (question: FormQuestion, response = currentResponse) => {
    const PatternSchema = getFormQuestionPatternSchema(
      question,
      response,
      Schema,
    )

    return getFormQuestionStatus(question, response, PatternSchema)
  }
}

function useFormQuestionStatus(question: FormQuestion) {
  const [response] = useFormResponse()
  const Schema = useFormQuestionPatternSchema(question)

  return getFormQuestionStatus(question, response, Schema)
}

function useFormReferenceDocuments(): ReferenceDocument[] {
  return useFormContext().referenceDocuments
}

function useFormMRData(): FormMRData {
  const context = use(FormMRDataContext)

  console.log('FormMRDataContext', context)

  invariant(context, 'Must be used within a provider')

  return context
}

export {
  addSignatureHistoryEntry,
  FormResponse,
  FormSignatures,
  getFormHTMLExport,
  sign,
  useCompleteForm,
  useForm,
  useFormMRData,
  useFormQuestionPatternSchema,
  useFormQuestionStatus,
  useFormRailcar,
  useFormReferenceDocuments,
  useFormResponse,
  useFormServiceEvent,
  useFormSignatures,
  useGetFormQuestionStatus,
  useHiddenQuestionIds,
  useQuestionHandle,
  useSetFormContent,
  useSignFormSection,
  useValidateForm,
}
export default FormProvider
